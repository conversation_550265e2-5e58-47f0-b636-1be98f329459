<Window x:Class="ExamPaperEditingSystem.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExamPaperEditingSystem.Converters"
        mc:Ignorable="d"
        Title="AI试卷编辑系统" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Icon="../icon.svg">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="{StaticResource SecondaryBrush}">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="新建" Command="{Binding CreateExamPaperCommand}"/>
                <MenuItem Header="打开" Command="{Binding LoadExamPaperCommand}"/>
                <MenuItem Header="保存" Command="{Binding SaveExamPaperCommand}"/>
                <MenuItem Header="导出" Command="{Binding ExportExamPaperCommand}"/>
                <Separator/>
                <MenuItem Header="退出" Click="ExitMenuItem_Click"/>
            </MenuItem>
            <MenuItem Header="编辑(_E)">
                <MenuItem Header="导入文档" Command="{Binding ImportDocumentsCommand}"/>
                <MenuItem Header="创建知识库" Command="{Binding CreateKnowledgeBaseCommand}"/>
                <MenuItem Header="删除知识库" Command="{Binding DeleteKnowledgeBaseCommand}"/>
                <MenuItem Header="清空聊天" Command="{Binding ClearChatCommand}"/>
            </MenuItem>
            <MenuItem Header="设置(_S)">
                <MenuItem Header="模型配置" Click="ModelConfigMenuItem_Click"/>
                <MenuItem Header="API设置" Click="ApiSettingsMenuItem_Click"/>
                <MenuItem Header="界面设置" Click="UISettingsMenuItem_Click"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="使用说明" Click="HelpMenuItem_Click"/>
                <MenuItem Header="关于" Click="AboutMenuItem_Click"/>
            </MenuItem>
        </Menu>
        
        <!-- 顶部工具栏 -->
        <Border Grid.Row="1" Background="{StaticResource PrimaryBrush}" Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="导入文档" Command="{Binding ImportDocumentsCommand}" Style="{StaticResource ModernButtonStyle}" Margin="5"/>
                <Button Content="创建知识库" Command="{Binding CreateKnowledgeBaseCommand}" Style="{StaticResource ModernButtonStyle}" Margin="5"/>
                <Button Content="创建试卷" Command="{Binding CreateExamPaperCommand}" Style="{StaticResource ModernButtonStyle}" Margin="5"/>
                <Button Content="保存试卷" Command="{Binding SaveExamPaperCommand}" Style="{StaticResource ModernButtonStyle}" Margin="5"/>
                <Button Content="导出试卷" Command="{Binding ExportExamPaperCommand}" Style="{StaticResource ModernButtonStyle}" Margin="5"/>
                <Button Content="清空聊天" Command="{Binding ClearChatCommand}" Style="{StaticResource ModernButtonStyle}" Margin="5"/>
            </StackPanel>
        </Border>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧面板 -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="10">
                <StackPanel>
                    <TextBlock Text="知识库管理" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                    
                    <TextBlock Text="选择知识库:" Margin="0,0,0,5"/>
                    <ComboBox ItemsSource="{Binding KnowledgeBases}" 
                              SelectedItem="{Binding SelectedKnowledgeBase}"
                              Style="{StaticResource ModernComboBoxStyle}"
                              Margin="0,0,0,15"/>
                    
                    <CheckBox Content="显示思维链" IsChecked="{Binding ShowThinkingChain}" Margin="0,0,0,15"/>
                    
                    <TextBlock Text="试卷管理" FontWeight="Bold" FontSize="16" Margin="0,15,0,10"/>
                    
                    <TextBlock Text="当前试卷:" Margin="0,0,0,5"/>
                    <ComboBox ItemsSource="{Binding ExamPapers}" 
                              SelectedItem="{Binding CurrentExamPaper}"
                              DisplayMemberPath="Title"
                              Style="{StaticResource ModernComboBoxStyle}"
                              Margin="0,0,0,15"/>
                    
                    <TextBlock Text="试卷信息" FontWeight="Bold" FontSize="14" Margin="0,15,0,10"/>
                    <StackPanel DataContext="{Binding CurrentExamPaper}" IsEnabled="{Binding DataContext.CurrentExamPaper, RelativeSource={RelativeSource AncestorType=Window}, Converter={x:Static local:BooleanConverter.IsNotNull}}">
                        <TextBlock Text="标题:" Margin="0,0,0,2"/>
                        <TextBox Text="{Binding Title}" Style="{StaticResource ModernTextBoxStyle}" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="科目:" Margin="0,0,0,2"/>
                        <TextBox Text="{Binding Subject}" Style="{StaticResource ModernTextBoxStyle}" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="创建时间:" Margin="0,0,0,2"/>
                        <TextBlock Text="{Binding CreatedDate, StringFormat=yyyy-MM-dd HH:mm}" Margin="0,0,0,10"/>
                    </StackPanel>
                </StackPanel>
            </Border>
            
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Stretch" Background="{StaticResource BorderBrush}"/>
            
            <!-- 中间聊天区域 -->
            <Border Grid.Column="2" Style="{StaticResource CardStyle}" Margin="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="AI对话" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                    
                    <!-- 聊天消息列表 -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,0,0,10">
                        <ItemsControl ItemsSource="{Binding ChatMessages}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Margin="5" Padding="10" 
                                            Background="{Binding Role, Converter={x:Static local:RoleToBackgroundConverter.Instance}}"
                                            CornerRadius="8">
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                                <TextBlock Text="{Binding Role, Converter={x:Static local:RoleToDisplayConverter.Instance}}" 
                                                           FontWeight="Bold" Margin="0,0,10,0"/>
                                                <TextBlock Text="{Binding Timestamp, StringFormat=HH:mm:ss}" 
                                                           Foreground="Gray" FontSize="12"/>
                                                <Button Content="复制" Command="{Binding DataContext.CopyMessageCommand, RelativeSource={RelativeSource AncestorType=Window}}" 
                                                        CommandParameter="{Binding}" 
                                                        Style="{StaticResource ModernButtonStyle}" 
                                                        FontSize="10" Padding="5,2" Margin="10,0,0,0"/>
                                                <Button Content="{Binding IsThinkingExpanded, Converter={x:Static local:ThinkingButtonTextConverter.Instance}}" 
                                                        Command="{Binding DataContext.ToggleThinkingCommand, RelativeSource={RelativeSource AncestorType=Window}}" 
                                                        CommandParameter="{Binding}" 
                                                        Style="{StaticResource ModernButtonStyle}" 
                                                        FontSize="10" Padding="5,2" Margin="5,0,0,0"
                                                        Visibility="{Binding IsThinking, Converter={x:Static local:BooleanToVisibilityConverter.Instance}}"/>
                                            </StackPanel>
                                            
                                            <!-- 思维链内容 -->
                                            <Border Background="LightYellow" Padding="8" CornerRadius="4" Margin="0,0,0,5"
                                                    Visibility="{Binding IsThinkingExpanded, Converter={x:Static local:BooleanToVisibilityConverter.Instance}}">
                                                <StackPanel>
                                                    <TextBlock Text="思维链:" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                                                    <TextBlock Text="{Binding ThinkingContent}" TextWrapping="Wrap" FontSize="11" Foreground="DarkOrange"/>
                                                </StackPanel>
                                            </Border>
                                            
                                            <!-- 主要内容 -->
                                            <TextBlock Text="{Binding Content}" TextWrapping="Wrap"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                    
                    <!-- 输入区域 -->
                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0" Text="{Binding CurrentMessage, UpdateSourceTrigger=PropertyChanged}" 
                                 Style="{StaticResource ModernTextBoxStyle}" 
                                 AcceptsReturn="True" TextWrapping="Wrap" 
                                 MaxHeight="100" VerticalScrollBarVisibility="Auto"
                                 KeyDown="MessageTextBox_KeyDown"/>
                        
                        <Button Grid.Column="1" Content="发送" 
                                Command="{Binding SendMessageCommand}" 
                                Style="{StaticResource ModernButtonStyle}" 
                                Margin="10,0,0,0" Padding="20,5"
                                IsEnabled="{Binding IsProcessing, Converter={x:Static local:InverseBooleanConverter.Instance}}"/>
                    </Grid>
                </Grid>
            </Border>
            
            <GridSplitter Grid.Column="3" Width="5" HorizontalAlignment="Stretch" Background="{StaticResource BorderBrush}"/>
            
            <!-- 右侧试卷编辑区域 -->
            <Border Grid.Column="4" Style="{StaticResource CardStyle}" Margin="10">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel DataContext="{Binding CurrentExamPaper}" 
                                IsEnabled="{Binding DataContext.CurrentExamPaper, RelativeSource={RelativeSource AncestorType=Window}, Converter={x:Static local:BooleanConverter.IsNotNull}}">
                        <TextBlock Text="试卷编辑" FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/>
                        
                        <TextBlock Text="试卷标题:" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding Title}" Style="{StaticResource ModernTextBoxStyle}" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="科目:" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding Subject}" Style="{StaticResource ModernTextBoxStyle}" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="考试时间:" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding ExamTime}" Style="{StaticResource ModernTextBoxStyle}" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="总分:" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding TotalScore}" Style="{StaticResource ModernTextBoxStyle}" Margin="0,0,0,15"/>
                        
                        <TextBlock Text="题目列表" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        
                        <ItemsControl ItemsSource="{Binding Questions}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="LightGray" Padding="10" Margin="0,0,0,10" CornerRadius="5">
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                                <TextBlock Text="题目 " FontWeight="Bold"/>
                                                <TextBlock Text="{Binding QuestionNumber}" FontWeight="Bold"/>
                                                <TextBlock Text=" - " FontWeight="Bold"/>
                                                <TextBlock Text="{Binding Type}" FontWeight="Bold"/>
                                                <TextBlock Text=" (" FontWeight="Bold"/>
                                                <TextBlock Text="{Binding Score}" FontWeight="Bold"/>
                                                <TextBlock Text="分)" FontWeight="Bold"/>
                                            </StackPanel>
                                            
                                            <TextBlock Text="题目内容:" Margin="0,0,0,2"/>
                                            <TextBox Text="{Binding Content}" Style="{StaticResource ModernTextBoxStyle}" 
                                                     AcceptsReturn="True" TextWrapping="Wrap" MinHeight="60" Margin="0,0,0,5"/>
                                            
                                            <TextBlock Text="答案:" Margin="0,0,0,2"/>
                                            <TextBox Text="{Binding Answer}" Style="{StaticResource ModernTextBoxStyle}" 
                                                     AcceptsReturn="True" TextWrapping="Wrap" MinHeight="40" Margin="0,0,0,5"/>
                                            
                                            <TextBlock Text="解析:" Margin="0,0,0,2"/>
                                            <TextBox Text="{Binding Explanation}" Style="{StaticResource ModernTextBoxStyle}" 
                                                     AcceptsReturn="True" TextWrapping="Wrap" MinHeight="40"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        
                        <Button Content="添加题目" Style="{StaticResource ModernButtonStyle}" 
                                Margin="0,10,0,0" HorizontalAlignment="Left"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
        
        <!-- 底部状态栏 -->
        <Border Grid.Row="2" Background="{StaticResource SecondaryBrush}" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="处理状态: " VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding IsProcessing, Converter={x:Static local:ProcessingStatusConverter.Instance}}" 
                               VerticalAlignment="Center" Margin="0,0,20,0"/>
                    <TextBlock Text="知识库: " VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding SelectedKnowledgeBase}" VerticalAlignment="Center" 
                               Foreground="{StaticResource PrimaryBrush}" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>