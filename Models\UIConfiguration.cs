using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ExamPaperEditingSystem.Models
{
    /// <summary>
    /// 界面配置模型类
    /// </summary>
    public class UIConfiguration : INotifyPropertyChanged
    {
        private string _theme = "默认";
        private double _fontSize = 12.0;
        private string _language = "中文";
        private bool _showStatusBar = true;
        private bool _showToolbar = true;
        private bool _enableAnimations = true;
        private string _windowLayout = "标准";

        /// <summary>
        /// 主题
        /// </summary>
        public string Theme
        {
            get => _theme;
            set
            {
                _theme = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 字体大小
        /// </summary>
        public double FontSize
        {
            get => _fontSize;
            set
            {
                _fontSize = Math.Max(8.0, Math.Min(24.0, value));
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 语言
        /// </summary>
        public string Language
        {
            get => _language;
            set
            {
                _language = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 显示状态栏
        /// </summary>
        public bool ShowStatusBar
        {
            get => _showStatusBar;
            set
            {
                _showStatusBar = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 显示工具栏
        /// </summary>
        public bool ShowToolbar
        {
            get => _showToolbar;
            set
            {
                _showToolbar = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 启用动画
        /// </summary>
        public bool EnableAnimations
        {
            get => _enableAnimations;
            set
            {
                _enableAnimations = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 窗口布局
        /// </summary>
        public string WindowLayout
        {
            get => _windowLayout;
            set
            {
                _windowLayout = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        /// <returns></returns>
        public static UIConfiguration CreateDefault()
        {
            return new UIConfiguration();
        }

        /// <summary>
        /// 复制配置
        /// </summary>
        /// <returns></returns>
        public UIConfiguration Clone()
        {
            return new UIConfiguration
            {
                Theme = this.Theme,
                FontSize = this.FontSize,
                Language = this.Language,
                ShowStatusBar = this.ShowStatusBar,
                ShowToolbar = this.ShowToolbar,
                EnableAnimations = this.EnableAnimations,
                WindowLayout = this.WindowLayout
            };
        }
    }
}
