<Window x:Class="ExamPaperEditingSystem.Views.UIConfigurationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="界面设置" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Icon="../icon.svg">
    
    <Window.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,15,0,10"/>
        </Style>
        
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="0,5,0,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="InputStyle" TargetType="Control">
            <Setter Property="Margin" Value="0,2,0,8"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 主要内容区域 -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- 外观设置 -->
                <TextBlock Text="外观设置" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#ECF0F1" BorderThickness="1" Padding="15" Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 左列 -->
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <TextBlock Text="主题:" Style="{StaticResource LabelStyle}"/>
                            <ComboBox SelectedItem="{Binding Theme}" Style="{StaticResource InputStyle}">
                                <ComboBoxItem Content="默认"/>
                                <ComboBoxItem Content="深色"/>
                                <ComboBoxItem Content="浅色"/>
                                <ComboBoxItem Content="蓝色"/>
                            </ComboBox>
                            
                            <TextBlock Text="语言:" Style="{StaticResource LabelStyle}"/>
                            <ComboBox SelectedItem="{Binding Language}" Style="{StaticResource InputStyle}">
                                <ComboBoxItem Content="中文"/>
                                <ComboBoxItem Content="English"/>
                            </ComboBox>
                        </StackPanel>
                        
                        <!-- 右列 -->
                        <StackPanel Grid.Column="1" Margin="10,0,0,0">
                            <TextBlock Text="字体大小:" Style="{StaticResource LabelStyle}"/>
                            <Slider Value="{Binding FontSize}" Minimum="8" Maximum="24" TickFrequency="1" 
                                    IsSnapToTickEnabled="True" Margin="0,2,0,5"/>
                            <TextBlock Text="{Binding FontSize, StringFormat=F0}" HorizontalAlignment="Center" FontSize="10"/>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <!-- 界面布局 -->
                <TextBlock Text="界面布局" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#ECF0F1" BorderThickness="1" Padding="15" Margin="0,0,0,10">
                    <StackPanel>
                        <TextBlock Text="窗口布局:" Style="{StaticResource LabelStyle}"/>
                        <ComboBox SelectedItem="{Binding WindowLayout}" Style="{StaticResource InputStyle}" Margin="0,2,0,15">
                            <ComboBoxItem Content="标准"/>
                            <ComboBoxItem Content="紧凑"/>
                            <ComboBoxItem Content="宽屏"/>
                        </ComboBox>
                        
                        <CheckBox Content="显示工具栏" IsChecked="{Binding ShowToolbar}" Margin="0,5"/>
                        <CheckBox Content="显示状态栏" IsChecked="{Binding ShowStatusBar}" Margin="0,5"/>
                        <CheckBox Content="启用动画效果" IsChecked="{Binding EnableAnimations}" Margin="0,5"/>
                    </StackPanel>
                </Border>
                
                <!-- 预览区域 -->
                <TextBlock Text="预览" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#ECF0F1" BorderThickness="1" Padding="15" Margin="0,0,0,10" Background="#F8F9FA">
                    <StackPanel>
                        <TextBlock Text="这是一个预览示例" FontSize="{Binding FontSize}" Margin="0,5"/>
                        <TextBlock Text="字体大小和主题将在这里显示效果" FontSize="{Binding FontSize}" Foreground="#7F8C8D" Margin="0,5"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0" Visibility="{Binding ShowToolbar, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Button Content="示例按钮1" Style="{StaticResource ButtonStyle}" Background="#E74C3C" IsEnabled="False"/>
                            <Button Content="示例按钮2" Style="{StaticResource ButtonStyle}" Background="#27AE60" IsEnabled="False"/>
                            <Button Content="示例按钮3" Style="{StaticResource ButtonStyle}" Background="#F39C12" IsEnabled="False"/>
                        </StackPanel>
                        
                        <Border Background="#34495E" Height="20" Margin="0,10,0,0" 
                                Visibility="{Binding ShowStatusBar, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <TextBlock Text="状态栏示例" Foreground="White" VerticalAlignment="Center" Margin="5,0"/>
                        </Border>
                    </StackPanel>
                </Border>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- 底部按钮 -->
        <Border Grid.Row="1" BorderBrush="#ECF0F1" BorderThickness="0,1,0,0" Padding="0,15,0,0" Margin="0,15,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="重置默认" Command="{Binding ResetDefaultCommand}" Style="{StaticResource ButtonStyle}" Background="#E74C3C"/>
                <Button Content="应用" Command="{Binding ApplyCommand}" Style="{StaticResource ButtonStyle}" Background="#F39C12"/>
                <Button Content="取消" Command="{Binding CancelCommand}" Style="{StaticResource ButtonStyle}" Background="#95A5A6"/>
                <Button Content="确定" Command="{Binding SaveCommand}" Style="{StaticResource ButtonStyle}" Background="#27AE60"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
