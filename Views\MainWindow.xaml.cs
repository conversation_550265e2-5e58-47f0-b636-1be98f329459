using System.Windows;
using System.Windows.Input;
using ExamPaperEditingSystem.ViewModels;
using ExamPaperEditingSystem.Services;
using Microsoft.Extensions.DependencyInjection;

namespace ExamPaperEditingSystem.Views
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly IConfigurationService _configurationService;

        public MainWindow(IConfigurationService configurationService)
        {
            InitializeComponent();
            _configurationService = configurationService;
        }

        private void MessageTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && !Keyboard.Modifiers.HasFlag(ModifierKeys.Shift))
            {
                e.Handled = true;
                var viewModel = DataContext as MainWindowViewModel;
                if (viewModel?.SendMessageCommand?.CanExecute(null) == true)
                {
                    viewModel.SendMessageCommand.Execute(null);
                }
            }
        }

        // 菜单事件处理程序
        private void ExitMenuItem_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void ModelConfigMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var configWindow = new AIConfigurationWindow(_configurationService);
                configWindow.Owner = this;
                configWindow.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"打开AI配置窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApiSettingsMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var configWindow = new AIConfigurationWindow(_configurationService);
                configWindow.Owner = this;
                configWindow.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"打开API设置窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UISettingsMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var uiConfigWindow = new UIConfigurationWindow(_configurationService);
                uiConfigWindow.Owner = this;
                uiConfigWindow.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"打开界面设置窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void HelpMenuItem_Click(object sender, RoutedEventArgs e)
        {
            var helpText = @"AI试卷编辑系统使用说明

📚 基本功能：
1. 导入文档：选择课件文档创建知识库
2. 创建知识库：处理文档并建立向量数据库
3. 创建试卷：基于知识库生成试卷
4. AI对话：与AI助手交流完善试卷
5. 导出试卷：将试卷导出为Word文档

⚙️ 配置说明：
• 模型配置：设置AI模型、API地址、参数等
• API设置：配置在线和本地API端点
• 界面设置：调整主题、字体、布局等

🔧 API配置：
• 支持在线和本地API切换
• 可测试API连接状态
• 支持代理设置

💡 使用技巧：
• 优先使用本地API可提高响应速度
• 调整温度参数可控制AI创造性
• 使用思维链可查看AI推理过程";

            MessageBox.Show(helpText, "使用说明", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AboutMenuItem_Click(object sender, RoutedEventArgs e)
        {
            var aboutText = @"🤖 AI试卷编辑系统
版本：1.0.0
构建日期：2024年12月

📋 功能特性：
• 智能文档处理和知识库构建
• 基于向量数据库的语义检索
• AI驱动的试卷生成和优化
• 多模型支持（在线/本地API）
• 可配置的界面和参数设置

🛠️ 技术架构：
• .NET 6 + WPF界面框架
• 依赖注入和MVVM模式
• JSON配置管理
• HTTP客户端API调用

👥 开发团队：AI教育技术团队
📧 技术支持：<EMAIL>";

            MessageBox.Show(aboutText, "关于 AI试卷编辑系统", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}