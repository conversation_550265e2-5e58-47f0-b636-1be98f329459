using System.Windows;
using System.Windows.Input;
using ExamPaperEditingSystem.ViewModels;
using ExamPaperEditingSystem.Services;
using Microsoft.Extensions.DependencyInjection;

namespace ExamPaperEditingSystem.Views
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly IConfigurationService _configurationService;

        public MainWindow(IConfigurationService configurationService)
        {
            InitializeComponent();
            _configurationService = configurationService;
        }

        private void MessageTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && !Keyboard.Modifiers.HasFlag(ModifierKeys.Shift))
            {
                e.Handled = true;
                var viewModel = DataContext as MainWindowViewModel;
                if (viewModel?.SendMessageCommand?.CanExecute(null) == true)
                {
                    viewModel.SendMessageCommand.Execute(null);
                }
            }
        }

        // 菜单事件处理程序
        private void ExitMenuItem_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void ModelConfigMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var configWindow = new AIConfigurationWindow(_configurationService);
                configWindow.Owner = this;
                configWindow.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"打开AI配置窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApiSettingsMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var configWindow = new AIConfigurationWindow(_configurationService);
                configWindow.Owner = this;
                configWindow.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"打开API设置窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UISettingsMenuItem_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("界面设置功能\n\n这里可以配置：\n• 主题颜色\n• 字体大小\n• 窗口布局\n• 语言设置", "界面设置", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void HelpMenuItem_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("AI试卷编辑系统使用说明\n\n1. 导入文档：选择课件文档创建知识库\n2. 创建知识库：处理文档并建立向量数据库\n3. 创建试卷：基于知识库生成试卷\n4. AI对话：与AI助手交流完善试卷\n5. 导出试卷：将试卷导出为Word文档", "使用说明", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AboutMenuItem_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("AI试卷编辑系统\n版本：1.0.0\n\n基于人工智能技术的智能试卷编辑工具\n支持文档导入、知识库构建、智能问答生成\n\n开发团队：AI教育技术团队", "关于", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}